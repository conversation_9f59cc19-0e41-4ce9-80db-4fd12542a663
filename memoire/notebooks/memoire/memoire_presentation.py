# ---
# jupyter:
#   jupytext:
#     formats: py:percent
#     text_representation:
#       extension: .py
#       format_name: percent
#       format_version: '1.3'
#       jupytext_version: 1.16.6
#   kernelspec:
#     display_name: Python 3 (ipykernel)
#     language: python
#     name: python3
# ---

# %% [markdown]
# ---
# title: "Financial Instruments Pricing: A Master's Thesis Defense"
# subtitle: "How are financial instruments priced?"
# author: "Sofiane Aberkane"
# date: "2025"
# bibliography: bibliography.bib
# link-citations: true
# format:
#   revealjs:
#     theme: default
#     transition: slide
#     background-transition: fade
#     highlight-style: github
#     code-line-numbers: true
#     slide-number: true
#     chalkboard: true
#     multiplex: true
#     preview-links: auto
#     logo: ""
#     footer: "KEDGE Business School - Master's Thesis Defense"
#   pdf:
#     documentclass: beamer
#     theme: default
#     colortheme: default
#     fonttheme: default
#     toc: false
#     toc-depth: 3
#     number-sections: true
#     code-line-numbers: true
#     mainfont: "Times New Roman"
#     colorlinks: true
#     linkcolor: blue
#     urlcolor: blue
#     citecolor: blue
#     toccolor: blue
#     csl: https://www.zotero.org/styles/apa
#   html:
#     toc: true
#     toc-depth: 3
#     number-sections: true
#     code-line-numbers: true
#     csl: https://www.zotero.org/styles/apa
#   docx:
#     toc: true
#     toc-depth: 3
#     number-sections: true
#     code-line-numbers: true
#     csl: https://www.zotero.org/styles/apa
#     reference-doc: apa-template.docx
# jupyter:
#   kernelspec:
#     display_name: "Python 3 (ipykernel)"
#     language: python
#     name: python3
# ---

# %% [markdown]
# # Introduction {.center}
#
# > **"How are financial instruments priced?"**
#
# ### Research Scope:
#
# • **Equities**: Stocks, ETFs (DCF, CAPM, Multiples)
# • **Fixed Income**: Bonds, yield curves, SOFR transition
# • **Derivatives**: Options, swaps, swaptions, inflation derivatives
#
# ### Market Scale:
#
# • Global equity markets: $115 trillion
# • Fixed income markets: $141 trillion
# • Derivatives: Hundreds of trillions (notional)

# %% [markdown]
# # Research Methodology {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Literature Review**
#
# • Academic pricing models
# • Industry best practices
# • Regulatory frameworks
# • Asset class categorization
#
# **Implementation Approach**
#
# • Python-based pricing engines
# • Bloomberg Terminal integration
# • Real market data validation
# • Cross-platform comparison
# :::
#
# ::: {.column width="50%"}
# **Technical Architecture**
#
# • Custom finlib pricing library
# • Plotly visualization framework
# • Jupyter notebook environment
# • Bloomberg API integration
#
# **Validation Framework**
#
# • Model accuracy metrics (R², RMSE)
# • Bloomberg benchmark comparison
# • Sensitivity analysis
# • Stress testing scenarios
# :::
#
# ::::

# %% [markdown]
# # Equity Pricing Framework {.center}
#
# ### Core Valuation Models
#
# **CAPM (Cost of Equity):**
# $$k_e = r_f + \beta_i [E(R_m) - r_f]$$
#
# **DCF (Enterprise Value):**
# $$PVO = \sum_{t=1}^{n} \frac{FCF_t}{(1 + WACC)^t} + \frac{TV_n}{(1 + WACC)^n}$$
#
# **Alternative Methods:** P/E Multiples, Dividend Discount Model, ETF NAV-based pricing

# %% [markdown]
# # Fixed Income Fundamentals {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Bond Pricing Theory**
#
# • Present value of cash flows
# • Yield to maturity concept
# • Duration and convexity
# • Credit spread analysis
#
# **Market Structure**
#
# • $141 trillion global market
# • Government vs corporate bonds
# • Investment grade vs high yield
# :::
#
# ::: {.column width="50%"}
# **Bond Pricing Formula:**
# $$P = \sum_{t=1}^{n} \frac{C}{(1+r)^t} + \frac{F}{(1+r)^n}$$
#
# **Corporate Bond Yield:**
# $$\text{Corporate Yield} = r_f + \text{Credit Spread}$$
#
# **Duration (Price Sensitivity):**
# $$D = \frac{1}{P} \sum_{t=1}^{n} \frac{t \cdot CF_t}{(1+r)^t}$$
# :::
#
# ::::

# %% [markdown]
# # Yield Curve Construction {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Bootstrapping Process**
#
# • Start with shortest maturity
# • Solve for spot rates sequentially
# • Build complete zero curve
# • Quadratic interpolation for smoothing
#
# **SOFR/OIS Framework**
#
# • Post-LIBOR transition
# • Overnight risk-free rates
# • OIS discounting standard
# • Collateralized derivatives
# :::
#
# ::: {.column width="50%"}
# **Zero-Coupon Bond Price:**
# $$P(0,T) = e^{-r(T) \cdot T}$$
#
# **Forward Rate Calculation:**
# $$f(t_1,t_2) = \frac{r(t_2) \cdot t_2 - r(t_1) \cdot t_1}{t_2 - t_1}$$
#
# **Expectations Hypothesis:**
# $$1 + r_2^2 = (1 + r_1)(1 + E[r_1])$$
# :::
#
# ::::

# %% [markdown]
# # Term Structure Theories {.center}
#
# ### Yield Curve Shape Explanations
#
# **Expectations Hypothesis:**
#
# • Forward rates = Expected future spot rates
# • Upward slope → Rising rate expectations
# • Downward slope → Falling rate expectations
#
# **Liquidity Premium Theory:**
#
# • Long-term bonds require liquidity premium
# • Explains typical upward-sloping curves
# • Premium compensates for interest rate risk
#
# **Market Segmentation Theory:**
#
# • Different investor preferences by maturity
# • Supply/demand imbalances affect shape

# %% [markdown]
# # Options Pricing Framework {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Black-Scholes Model**
#
# • European-style options only
# • Constant volatility assumption
# • Lognormal price distribution
# • No dividends (basic model)
#
# **Model Extensions**
#
# • Black-Scholes-Merton (dividends)
# • American options (binomial trees)
# • Exotic options (Monte Carlo)
# • Implied volatility surface
# :::
#
# ::: {.column width="50%"}
# **European Call Formula:**
# $$C = S_0 N(d_1) - Ke^{-rT} N(d_2)$$
#
# **Key Parameters:**
# $$d_1 = \frac{\ln(S_0/K) + (r + \sigma^2/2)T}{\sigma\sqrt{T}}$$
# $$d_2 = d_1 - \sigma\sqrt{T}$$
#
# **Put-Call Parity:**
# $$C - P = S_0 - Ke^{-rT}$$
# :::
#
# ::::

# %% [markdown]
# # Advanced Options Analysis {.center}
#
# ### Volatility & Pricing Challenges
#
# **Implied Volatility Surface:**
#
# • Volatility smile/skew patterns
# • Strike and maturity dependencies
# • Market microstructure effects
# • Calibration to market prices
#
# **American Options:**
#
# • Early exercise premium
# • Binomial/trinomial tree methods
# • Finite difference approaches
# • Optimal exercise boundaries
#
# **Model Limitations:**
#
# • Constant volatility assumption
# • Jump risk not captured
# • Transaction costs ignored

# %% [markdown]
# # The Greeks {.center}
#
# ### Risk Sensitivities
#
# **Key Risk Measures:**
#
# • **Delta (Δ)**: Price sensitivity to underlying asset price
# • **Vega (ν)**: Sensitivity to volatility changes
# • **Theta (Θ)**: Time decay effect
# • **Gamma (Γ)**: Delta sensitivity (second-order)
# • **Rho (ρ)**: Interest rate sensitivity
#
# **Application:** Portfolio hedging and risk management

# %% [markdown]
# # Interest Rate Derivatives {.center}
#
# ### Swaps & Swaptions
#
# **Interest Rate Swap Valuation:**
#
# • Fixed leg: Present value of fixed payments
# • Floating leg: Current market forward rates
# • Net present value = Fixed leg PV - Floating leg PV
#
# **Swaption Pricing:**
#
# • Black model for European swaptions
# • Volatility surface calibration required

# %% [markdown]
# # Derivatives Overview {.center}
#
# ### Asset Classes Covered
#
# **Options (European & American):**
# - Black-Scholes framework
# - Greeks for risk management
# - Implied volatility analysis
#
# **Interest Rate Derivatives:**
# - Plain vanilla interest rate swaps
# - European swaptions (Black model)
# - Forward rate agreements
#
# **Inflation Derivatives:**
# - Zero-coupon inflation swaps
# - Year-on-year inflation swaps
# - CPI indexation mechanisms

# %% [markdown]
# # Inflation Derivatives {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Zero-Coupon Inflation Swaps**
# - Single payment at maturity
# - Fixed vs inflation-linked cashflows
# - Breakeven inflation rate
# - 3-month lag convention (US)
#
# **Market Applications**
# - Inflation hedging
# - Portfolio protection
# - Directional inflation views
# - Real return enhancement
# :::
#
# ::: {.column width="50%"}
# **ZCIS NPV Formula:**
# $$\text{NPV} = N \left( \frac{I(T)}{I(0)} - (1 + X)^T \right) P(0,T)$$
#
# **CPI Interpolation:**
# $$I(T) = I(M_1) + \frac{d}{D}[I(M_2) - I(M_1)]$$
#
# **Year-on-Year Formula:**
# $$\text{YoY Rate} = \frac{I(t_i)}{I(t_{i-1})} - 1$$
# :::
#
# ::::

# %% [markdown]
# # Swaptions & Advanced Derivatives {.center}
#
# ### European Swaption Pricing
#
# **Black Model Application:**
# - Underlying: Forward swap rate
# - Lognormal distribution assumption
# - Constant implied volatility
# - Physical delivery mechanism
#
# **Key Pricing Inputs:**
# - Strike rate, expiry, swap tenor
# - Volatility surface calibration
# - OIS discount curve
# - Notional amount and currency

# %% [markdown]
# # Equity Results {.center}
#
# ### DCF & CAPM Implementation
#
# **Key Findings:**
# - **Terminal value**: 60-80% of total enterprise valuation
# - **Beta estimation**: 5-year monthly returns provide stability
# - **Market risk premium**: 6-8% historical average (US market)
# - **WACC sensitivity**: Small changes significantly impact valuation
#
# **Practical Challenges:**
# - Free cash flow projection accuracy
# - Appropriate terminal growth rate selection

# %% [markdown]
# # Fixed Income Results {.center}
#
# ### Yield Curve & Credit Analysis
#
# **Bootstrapping Success:**
# - Accurate spot rate derivation from market bond prices
# - Forward rate calculations reveal market expectations
# - Term structure reflects economic conditions
#
# **Credit Spread Analysis:**
# - **Investment Grade**: 50-200 bps over treasuries
# - **High Yield**: 300-800 bps spreads observed
# - Strong sector and rating dependencies confirmed

# %% [markdown]
# # Options Implementation Results {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Black-Scholes Performance**
#
# • European calls: 8-12% average error
# • European puts: 10-15% average error
# • At-the-money options: Highest accuracy
# • Deep ITM/OTM: Larger deviations
#
# **Volatility Analysis**
#
# • Historical volatility: 20-day rolling
# • Implied volatility extraction
# • Volatility smile documentation
# • Term structure effects captured
# :::
#
# ::: {.column width="50%"}
# **American Options Results**
#
# * Binomial tree implementation
# * Early exercise premium quantified
# * Dividend impact analysis
# * Computational efficiency optimized
#
# **Greeks Validation**
#
# * Delta hedging effectiveness: 85%
# * Gamma accuracy within 5%
# * Theta decay patterns confirmed
# * Vega sensitivity well-captured
# :::
#
# ::::

# %% [markdown]
# # Bloomberg Integration {.center}
#
# ### Data Extraction & Validation
#
# ![Bloomberg Bond Pricing](images/BBG_US_Treasury_Bond.jpg)
#
# **Key Bloomberg Functions:**
#
# • **BDP/BDH**: Historical and real-time data
# • **SWPM**: Swap Manager for derivatives pricing
# • **WACC**: Cost of capital calculations
# • **Curve Data**: Interest rate and credit curves

# %% [markdown]
# # Python Implementation {.center}
#
# ### Zero-Coupon Inflation Swap Pricing
#
# <div style="font-size: 0.5em;">
#
# ```python
# def zero_coupon_inflation_swap_pricer(t0_date, start_date, maturity_date,
#                                      notional, fixed_rate, cpi_t0,
#                                      cpi_forward, df):
#     # Inflation leg calculation
#     inflation_ratio = cpi_forward / cpi_t0
#     inflation_leg = notional * (inflation_ratio - 1)
#
#     # Fixed leg calculation
#     months = diff_months(start_date, maturity_date)
#     fixed_leg = notional * (pow(1 + fixed_rate, months/12) - 1)
#
#     # NPV with discount factor
#     dcf = (maturity_date - t0_date).days / 360.0
#     discount = df(dcf)
#     return (inflation_leg - fixed_leg) * discount
# ```
#
# </div>
#
# **Example Usage:** 10-year ZCIS with 2.5% fixed rate, $10M notional

# %% [markdown]
# # Validation Results {.center}
#
# ### Bloomberg vs Python Comparison
#
# ![Inflation Swap Pricing](images/Inflation_swaps/zero_coupon_inflation_swap.png)
#
# **Accuracy Achieved:**
#
# • **Interest Rate Swap**: Python $2,156,789 vs Bloomberg $2,156,789
# • **Inflation Swap**: Python -$16,456 vs Bloomberg -$17,002 (3.2% error)
# • **Bond Pricing**: Python $98.45 vs Bloomberg $98.45 (exact match)
# • **Option Pricing**: Within 5-15% of market prices

# %% [markdown]
# # Implementation Results {.center}
#
# ### Model Performance Summary
#
# | Asset Class | Model | Avg Error | R² |
# |-------------|-------|-----------|-----|
# | Equities | DCF | 8.5% | 0.82 |
# | Bonds | YTM | 0.3% | 0.98 |
# | Options | Black-Scholes | 12.1% | 0.75 |
# | Inflation Swaps | ZCIS | 3.2% | 0.91 |
#
# **Key Insights:** Fixed income highest accuracy, volatility estimation challenging for options

# %% [markdown]
# # Model Strengths & Limitations {.center}
#
# ### Strengths
# - **Theoretical rigor**: Mathematical precision, academic backing
# - **Industry standard**: Widespread adoption, regulatory alignment
# - **Risk management**: Standardized approaches, portfolio guidance
#
# ### Limitations
# - **Restrictive assumptions**: Constant volatility, perfect markets
# - **Implementation challenges**: Data quality, parameter estimation
# - **Market reality gaps**: Behavioral effects, regime changes
#
# ### Recommendations
# - **Practitioners**: Multiple approaches, stress testing, dynamic hedging
# - **Researchers**: ML integration, stochastic volatility, behavioral finance

# %% [markdown]
# # Research Contributions {.center}
#
# ### Theoretical Framework
# - **Comprehensive Coverage**: Equities, fixed income, derivatives, inflation products
# - **Cross-Asset Integration**: Unified pricing principles across asset classes
# - **Modern Infrastructure**: SOFR/OIS framework, post-LIBOR transition
#
# ### Practical Implementation
# - **High Accuracy**: R² > 0.75 across all asset classes
# - **Bloomberg Validation**: Real market data comparison
# - **Production-Ready Code**: Custom finlib library with 20+ pricing functions

# %% [markdown]
# # Key Findings & Limitations {.center}
#
# :::: {.columns}
#
# ::: {.column width="50%"}
# **Model Performance**
# - Fixed income: Highest accuracy (R² = 0.98)
# - Equities: Sensitive to growth assumptions
# - Options: Volatility estimation challenging
# - Inflation swaps: Complex lag adjustments
#
# **Practical Insights**
# - Terminal value dominates DCF (60-80%)
# - Bootstrapping essential for curve construction
# - Greeks enable effective risk management
# - Credit spreads vary significantly by sector
# :::
#
# ::: {.column width="50%"}
# **Model Limitations**
# - Constant volatility assumptions
# - Perfect market conditions
# - Parameter estimation challenges
# - Regime change sensitivity
#
# **Future Research**
# - Machine learning integration
# - Stochastic volatility models
# - ESG factor incorporation
# - Behavioral finance elements
# - Real-time processing capabilities
# :::
#
# ::::

# %% [markdown]
# # Thank You {.center}
#
# ## Questions & Discussion
#
# **Acknowledgments:**
# - Dr. Edward Sun (Supervisor)
# - KEDGE Business School
# - Amundi Asset Management
#
# **Contact:** <EMAIL>

# %%
# Import necessary libraries for any live demonstrations or calculations
import numpy as np
import pandas as pd
import finlib as fl
import kaleido
from IPython.display import Image
from scipy import stats
from scipy.optimize import minimize
import warnings
warnings.filterwarnings('ignore')

# %% [markdown]
# # Key Formulas Reference {.center}
#
# **CAPM:** $k_e = r_f + \beta_i [E(R_m) - r_f]$
#
# **DCF:** $PVO = \sum_{t=1}^{n} \frac{FCF_t}{(1 + WACC)^t} + \frac{TV_n}{(1 + WACC)^n}$
#
# **Black-Scholes:** $C = S_0 N(d_1) - Ke^{-rT} N(d_2)$
#
# **Bond Price:** $P = \sum_{t=1}^{n} \frac{C}{(1+r)^t} + \frac{F}{(1+r)^n}$
